import { useCallback, useMemo, useRef, useEffect, useState } from 'react';

// Performance monitoring for memoization effectiveness
interface MemoizationStats {
  hitCount: number;
  missCount: number;
  hitRate: number;
  lastAccess: number;
}

class MemoizationMonitor {
  private static instance: MemoizationMonitor;
  private stats: Map<string, MemoizationStats> = new Map();

  static getInstance(): MemoizationMonitor {
    if (!MemoizationMonitor.instance) {
      MemoizationMonitor.instance = new MemoizationMonitor();
    }
    return MemoizationMonitor.instance;
  }

  recordHit(key: string) {
    const stats = this.stats.get(key) || { hitCount: 0, missCount: 0, hitRate: 0, lastAccess: 0 };
    stats.hitCount++;
    stats.lastAccess = Date.now();
    stats.hitRate = stats.hitCount / (stats.hitCount + stats.missCount);
    this.stats.set(key, stats);
  }

  recordMiss(key: string) {
    const stats = this.stats.get(key) || { hitCount: 0, missCount: 0, hitRate: 0, lastAccess: 0 };
    stats.missCount++;
    stats.lastAccess = Date.now();
    stats.hitRate = stats.hitCount / (stats.hitCount + stats.missCount);
    this.stats.set(key, stats);
  }

  getStats(key: string): MemoizationStats | undefined {
    return this.stats.get(key);
  }

  getAllStats(): Map<string, MemoizationStats> {
    return new Map(this.stats);
  }

  clearStats() {
    this.stats.clear();
  }
}

const monitor = MemoizationMonitor.getInstance();

/**
 * Enhanced useCallback with performance monitoring
 */
export function useOptimizedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  debugKey?: string
): T {
  const key = debugKey || `callback-${Math.random().toString(36).substr(2, 9)}`;
  const previousDeps = useRef<React.DependencyList>();
  
  const memoizedCallback = useCallback(callback, deps);
  
  useEffect(() => {
    if (previousDeps.current) {
      const depsChanged = deps.some((dep, index) => dep !== previousDeps.current![index]);
      if (depsChanged) {
        monitor.recordMiss(key);
      } else {
        monitor.recordHit(key);
      }
    }
    previousDeps.current = deps;
  }, deps);

  return memoizedCallback;
}

/**
 * Enhanced useMemo with performance monitoring and cache size management
 */
export function useMemoizedValue<T>(
  factory: () => T,
  deps: React.DependencyList,
  debugKey?: string
): T {
  const key = debugKey || `memo-${Math.random().toString(36).substr(2, 9)}`;
  const previousDeps = useRef<React.DependencyList>();
  const computationCount = useRef(0);
  
  const memoizedValue = useMemo(() => {
    computationCount.current++;
    return factory();
  }, deps);
  
  useEffect(() => {
    if (previousDeps.current) {
      const depsChanged = deps.some((dep, index) => dep !== previousDeps.current![index]);
      if (depsChanged) {
        monitor.recordMiss(key);
      } else {
        monitor.recordHit(key);
      }
    }
    previousDeps.current = deps;
  }, deps);

  return memoizedValue;
}

/**
 * Hook for expensive computations with automatic performance monitoring
 */
export function useExpensiveComputation<T>(
  computation: () => T,
  deps: React.DependencyList,
  options: {
    debugKey?: string;
    threshold?: number; // ms - warn if computation takes longer
    enableProfiling?: boolean;
  } = {}
): T {
  const { debugKey, threshold = 10, enableProfiling = process.env.NODE_ENV === 'development' } = options;
  const key = debugKey || `expensive-${Math.random().toString(36).substr(2, 9)}`;
  
  return useMemoizedValue(() => {
    if (enableProfiling) {
      const startTime = performance.now();
      const result = computation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > threshold) {
        console.warn(`Expensive computation "${key}" took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    }
    
    return computation();
  }, deps, key);
}

/**
 * Hook for debounced values with memoization
 */
export function useDebouncedMemo<T>(
  value: T,
  delay: number,
  debugKey?: string
): T {
  const [debouncedValue, setDebouncedValue] = useState(value);
  const key = debugKey || `debounced-${Math.random().toString(36).substr(2, 9)}`;

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      monitor.recordMiss(key);
    }, delay);

    return () => {
      clearTimeout(handler);
      monitor.recordHit(key);
    };
  }, [value, delay, key]);

  return debouncedValue;
}

/**
 * Hook for memoizing object/array dependencies to prevent unnecessary re-renders
 */
export function useStableDeps<T extends Record<string, any> | any[]>(
  deps: T,
  debugKey?: string
): T {
  const key = debugKey || `stable-${Math.random().toString(36).substr(2, 9)}`;
  
  return useMemoizedValue(() => deps, [JSON.stringify(deps)], key);
}

/**
 * Hook for performance monitoring of component renders
 */
export function useRenderPerformance(componentName: string) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(0);
  const totalRenderTime = useRef(0);

  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      renderCount.current++;
      lastRenderTime.current = renderTime;
      totalRenderTime.current += renderTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
        
        if (renderTime > 16) { // More than one frame at 60fps
          console.warn(`${componentName} slow render: ${renderTime.toFixed(2)}ms`);
        }
      }
    };
  });

  return {
    renderCount: renderCount.current,
    lastRenderTime: lastRenderTime.current,
    averageRenderTime: renderCount.current > 0 ? totalRenderTime.current / renderCount.current : 0,
  };
}

/**
 * Hook for memoizing search/filter operations
 */
export function useSearchMemo<T, R>(
  items: T[],
  searchTerm: string,
  filterFn: (items: T[], term: string) => R,
  options: {
    debounceMs?: number;
    minSearchLength?: number;
    debugKey?: string;
  } = {}
): R {
  const { debounceMs = 300, minSearchLength = 0, debugKey } = options;
  
  const debouncedSearchTerm = useDebouncedMemo(searchTerm, debounceMs, `${debugKey}-debounced`);
  
  return useMemoizedValue(
    () => {
      if (debouncedSearchTerm.length < minSearchLength) {
        return filterFn(items, '');
      }
      return filterFn(items, debouncedSearchTerm);
    },
    [items, debouncedSearchTerm, minSearchLength],
    `${debugKey}-search`
  );
}

/**
 * Hook for getting memoization statistics
 */
export function useMemoizationStats(key?: string) {
  const [stats, setStats] = useState<MemoizationStats | Map<string, MemoizationStats> | undefined>();

  useEffect(() => {
    const updateStats = () => {
      if (key) {
        setStats(monitor.getStats(key));
      } else {
        setStats(monitor.getAllStats());
      }
    };

    updateStats();
    const interval = setInterval(updateStats, 1000);

    return () => clearInterval(interval);
  }, [key]);

  return stats;
}

/**
 * Hook for clearing memoization statistics
 */
export function useClearMemoizationStats() {
  return useCallback(() => {
    monitor.clearStats();
  }, []);
}

/**
 * Higher-order hook for automatic performance optimization
 */
export function useAutoOptimize<T extends Record<string, any>>(
  props: T,
  componentName: string
): T {
  const renderPerf = useRenderPerformance(componentName);
  
  // Automatically memoize object props to prevent unnecessary re-renders
  const optimizedProps = useMemoizedValue(
    () => props,
    Object.values(props),
    `${componentName}-props`
  );

  // Log performance warnings in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && renderPerf.lastRenderTime > 16) {
      console.warn(
        `${componentName} may benefit from optimization. ` +
        `Last render: ${renderPerf.lastRenderTime.toFixed(2)}ms, ` +
        `Average: ${renderPerf.averageRenderTime.toFixed(2)}ms`
      );
    }
  }, [renderPerf.lastRenderTime, renderPerf.averageRenderTime, componentName]);

  return optimizedProps;
}
